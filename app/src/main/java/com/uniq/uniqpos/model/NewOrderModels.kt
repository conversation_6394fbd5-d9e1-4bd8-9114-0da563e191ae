package com.uniq.uniqpos.model

/**
 * Data models for the New Online Order dialog
 * These models are specifically designed for the incoming order notification dialog
 */

/**
 * Customer information for the new order
 */
data class Customer(
    val name: String,
    val phone: String
)

/**
 * Individual order item with modifiers and pricing
 */
data class OrderItem(
    val quantity: Int,
    val name: String,
    val modifiers: List<String>,
    val subtotal: Double
)

/**
 * Complete new order information
 * Note: Using "NewOrder" to avoid conflicts with existing Order model
 */
data class NewOrder(
    val id: String,
    val customer: Customer,
    val items: List<OrderItem>,
    val table: String?, // Nullable for orders without a table
    val total: Double
)
