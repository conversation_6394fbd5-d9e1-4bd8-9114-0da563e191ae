package com.uniq.uniqpos.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.net.toUri
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.sync.UniqSyncAdapter
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.ordersales.OrderSalesActivity
import timber.log.Timber
import java.io.File
import android.media.AudioAttributes
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.toLower

/**
 * Created by annasblackhat on 23/07/19
 */
class UniqMessagingService : FirebaseMessagingService() {

    val notificationChannelOrderSales = "Order Sales"

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Timber.i("receive new token: $token")
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Timber.i(">> From: ${remoteMessage.from}")
        remoteMessage.notification?.let {
            Timber.i(">> Message Notification Body: ${it.body}")
        }
        Timber.i(">> Data : ${remoteMessage.data}")
        remoteMessage.data.let { data ->
            when (data["type"]) {
                "action_upload_log" -> {
                    UniqSyncAdapter.syncImmediately(this, data["type"])
                }
                "action_sync" -> {
                    UniqSyncAdapter.syncImmediately(this, data["type"])
                }
                "action_check_version" -> {

                }
                "action_resync_cashrecap" -> {

                }
                "clear_old_sales" -> {
                    UniqSyncAdapter.syncImmediately(this, data["type"])
                }
                "order_sales" -> {
                    val intent = Intent(this, OrderSalesActivity::class.java)
                    intent.putExtra("phone", data["title"])
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)

                    val soundUri = this.sharedPref()
                        .getString(SharedPref.NOTIFICATION_PATH).apply {
                            Timber.i("notif sound path '$this'")
                        }?.
                        takeIf { it.isNotBlank()  }?.
                        let {  notifPath ->
                            File(notifPath).toUri()
                        }

//                    data[""]

                    Timber.i("push notif order_sales, soundUri null? ${soundUri == null}")

                    sendNotification(data["title"], data["message"], intent, soundUri = soundUri, notifChannel = "CRM Transaction")
                    broadcastSyncRequest("order_sales", mapOf("id" to data?.get("order_sales_id").safe()))
                }
                "new_version" -> {
                    val intent = Intent(
                        Intent.ACTION_VIEW,
                        Uri.parse("market://details?id=${BuildConfig.APPLICATION_ID}")
                    )
                    sendNotification(data["title"], data["message"], intent)
                }
                "open_link" -> {

                }
                "payment_receive" -> {
                    sendNotification(data["title"], data["message"])
                    broadcastSyncRequest("payment")
                }
                else -> {
                    sendNotification(data["title"], data["message"])
                }
            }
        }
    }

    private fun broadcastSyncRequest(dataType: String, metaData: Map<String, String>? = null) {
        val intentBroadcast = Intent(Constant.INTENT_SYNC_REQUEST)
        intentBroadcast.putExtra("sync", dataType)
        metaData?.forEach { (key, value) ->
            intentBroadcast.putExtra(key, value)
        }
        sendBroadcast(intentBroadcast)
    }

    private fun sendNotification(
        title: String?,
        messageBody: String?,
        intentParam: Intent? = null,
        soundUri: Uri? = null, //custom sound
        notifChannel: String = "System Notification"
    ) {
        val intent = intentParam ?: Intent(this, MainActivity::class.java)
//        val pendingIntent = PendingIntent.getActivity(
//            this, 0 /* Request code */, intent,
//            PendingIntent.FLAG_ONE_SHOT
//        )

        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_MUTABLE)
        } else {
            PendingIntent.getActivity(this, 0, intent,
                PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE)
        }

        val channelId = notifChannel.toLower().replace(" ", "_") // "chat.support"
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.ic_logo_uniq_white)
            .setContentTitle(title ?: "Checking New Message")
            .setContentText(messageBody ?: "You may have new message")
            .setAutoCancel(true)
            .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)

        //set custom sound
        soundUri?.let { notificationBuilder.setSound(it) }

        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Since android Oreo notification channel is needed.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                notifChannel,
                NotificationManager.IMPORTANCE_DEFAULT
            )
            soundUri?.let {
                val audioAttributes = AudioAttributes.Builder()
//                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                    .build()
                channel.setSound(soundUri, audioAttributes)
            }

            notificationManager.createNotificationChannel(channel)
        }

        var notifId = 10
        notificationManager.notify(notifId, notificationBuilder.build())
    }
}