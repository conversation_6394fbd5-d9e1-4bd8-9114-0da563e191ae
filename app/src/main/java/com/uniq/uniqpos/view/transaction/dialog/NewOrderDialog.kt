package com.uniq.uniqpos.view.transaction.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.safe
import java.text.NumberFormat
import java.util.*

/**
 * New Online Order Dialog using Jetpack Compose
 * Displays incoming order details with Accept/Reject actions
 */
@Composable
fun NewOrderDialog(
    orderSales: OrderSalesEntity,
    salesEntity: SalesEntity,
    products: List<ProductEntity>,
    onAccept: () -> Unit,
    onReject: () -> Unit,
    onDismissRequest: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.9f) // Limit dialog height to 90% of screen
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
            ) {
                // Scrollable content
                LazyColumn(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(24.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    item {
                        // Header with bell icon and title
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "🔔",
                                fontSize = 20.sp,
                                modifier = Modifier.padding(end = 8.dp)
                            )
                            Text(
                                text = "New Online Order",
                                fontSize = 20.sp,
                                fontWeight = FontWeight.SemiBold
                            )
                        }
                    }

                    item { Spacer(modifier = Modifier.height(8.dp)) }

                    item {
                        // Customer Information
                        Text(
                            text = salesEntity.customer.safe(),
                            fontSize = 22.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    item {
                        Text(
                            text = salesEntity.receiptReceiver.safe(),
                            fontSize = 16.sp,
                            color = Color.Gray
                        )
                    }

                    item { Spacer(modifier = Modifier.height(8.dp)) }

                    item {
                        // Order Metadata (Order ID and Table)
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Order #: ${salesEntity.displayNota}",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Normal
                            )
                            if (!salesEntity.table.isNullOrBlank()) {
                                Text(
                                    text = "Table: ${salesEntity.table}",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Normal
                                )
                            }
                        }
                    }

                    item { Spacer(modifier = Modifier.height(8.dp)) }
                    item { HorizontalDivider() }
                    item { Spacer(modifier = Modifier.height(8.dp)) }

                    item {
                        // Header Row for items
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "QTY",
                                fontSize = 12.sp,
                                color = Color.Gray,
                                modifier = Modifier.width(40.dp)
                            )
                            Text(
                                text = "ITEM",
                                fontSize = 12.sp,
                                color = Color.Gray,
                                modifier = Modifier.weight(1f)
                            )
                            Text(
                                text = "SUBTOTAL",
                                fontSize = 12.sp,
                                color = Color.Gray,
                                textAlign = TextAlign.End
                            )
                        }
                    }

                    item { Spacer(modifier = Modifier.height(8.dp)) }

                    // Items List - each item as a separate LazyColumn item
                    items(salesEntity.orderList ?: emptyList()) { item ->
                        OrderItemRow(item = item, products = products)
                    }

                    item { Spacer(modifier = Modifier.height(8.dp)) }
                    item { HorizontalDivider() }
                    item { Spacer(modifier = Modifier.height(8.dp)) }

                    item {
                        // Total Section
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Total",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = formatCurrency(salesEntity.grandTotal.toDouble()),
                                fontSize = 22.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }

                // Action Buttons - Fixed at bottom outside LazyColumn
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Reject Button
                    TextButton(
                        onClick = onReject,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Reject",
                            color = Color(0xFFD32F2F),
                            fontSize = 16.sp
                        )
                    }

                    // Accept Button
                    Button(
                        onClick = onAccept,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32)
                        )
                    ) {
                        Text(
                            text = "Accept",
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun OrderItemRow(item: Order, products: List<ProductEntity>) {
    // Find product details from the products list
    val product = products.find { it.productDetailId == item.product?.productDetailId }
    val productName = product?.name ?: item.product?.name ?: "Unknown Product"

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Top
    ) {
        // Quantity
        Text(
            text = item.qty.toString(),
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.width(40.dp)
        )

        // Item Details
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = productName,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )

            // Show modifiers/extras if any
            if (item.extra.isNotEmpty()) {
                val modifiers = item.extra.mapNotNull { extra ->
                    val extraProduct = products.find { it.productDetailId == extra.product?.productDetailId }
                    extraProduct?.name ?: extra.product?.name
                }
                if (modifiers.isNotEmpty()) {
                    Text(
                        text = "+ ${modifiers.joinToString(", ")}",
                        fontSize = 12.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(start = 8.dp, top = 2.dp)
                    )
                }
            }

            // Show note if any
            if (!item.note.isNullOrBlank()) {
                Text(
                    text = "Note: ${item.note}",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(start = 8.dp, top = 2.dp)
                )
            }
        }

        // Subtotal
        Text(
            text = formatCurrency(item.subTotal.toDouble()),
            fontSize = 14.sp,
            textAlign = TextAlign.End
        )
    }
}

private fun formatCurrency(amount: Double): String {
    val formatter = NumberFormat.getCurrencyInstance(Locale("id", "ID"))
    return formatter.format(amount)
}
