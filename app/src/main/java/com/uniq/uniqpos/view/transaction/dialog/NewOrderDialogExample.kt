package com.uniq.uniqpos.view.transaction.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity

/**
 * Example usage of NewOrderDialog composable
 * This demonstrates how to integrate the dialog in a Compose UI
 */
@Composable
fun NewOrderDialogExample() {
    // State to control dialog visibility
    var showDialog by remember { mutableStateOf(false) }

    // Sample order data using real entities
    val sampleOrderSales = remember {
        OrderSalesEntity(
            orderSalesId = "order_123",
            items = """{"customer":"<PERSON>","displayNota":"INV-OM2025092424","grandTotal":44450,"noNota":"","orderList":[{"price":12990,"qty":2,"subTotal":25980,"product_detail_fkid":15969,"product":{"product_detail_id":15969},"note":"Extra pickles, no onions"},{"price":6490,"qty":1,"subTotal":6490,"product_detail_fkid":39906,"product":{"product_detail_id":39906}},{"price":5990,"qty":2,"subTotal":11980,"product_detail_fkid":39907,"product":{"product_detail_id":39907}}],"sales_id":"","status":"","outletId":29,"unique_code":0,"order_type":"dine_in","table":"12A","receipt_receiver":"+1 (555) 123-4567"}""",
            orderType = "dine_in",
            status = "pending"
        )
    }

    val sampleSalesEntity = remember {
        val jsonString = """{"customer":"Eleanor Vance","displayNota":"INV-OM2025092424","grandTotal":44450,"noNota":"","orderList":[{"price":12990,"qty":2,"subTotal":25980,"product_detail_fkid":15969,"product":{"product_detail_id":15969},"note":"Extra pickles, no onions"},{"price":6490,"qty":1,"subTotal":6490,"product_detail_fkid":39906,"product":{"product_detail_id":39906}},{"price":5990,"qty":2,"subTotal":11980,"product_detail_fkid":39907,"product":{"product_detail_id":39907}}],"sales_id":"","status":"","outletId":29,"unique_code":0,"order_type":"dine_in","table":"12A","receipt_receiver":"+1 (555) 123-4567"}"""
        Gson().fromJson(jsonString, SalesEntity::class.java)
    }

    val sampleProducts = remember {
        listOf(
            ProductEntity(
                productId = 1,
                productDetailId = 15969,
                name = "Classic Cheeseburger",
                priceSell = 12990
            ),
            ProductEntity(
                productId = 2,
                productDetailId = 39906,
                name = "Large Fries",
                priceSell = 6490
            ),
            ProductEntity(
                productId = 3,
                productDetailId = 39907,
                name = "Vanilla Milkshake",
                priceSell = 5990
            )
        )
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Button(
            onClick = { showDialog = true }
        ) {
            Text("Show New Order Dialog")
        }
    }
    
    // Conditional dialog display
    if (showDialog) {
        NewOrderDialog(
            orderSales = sampleOrderSales,
            salesEntity = sampleSalesEntity,
            products = sampleProducts,
            onAccept = {
                println("Order accepted: ${sampleOrderSales.orderSalesId}")
                showDialog = false
            },
            onReject = {
                println("Order rejected: ${sampleOrderSales.orderSalesId}")
                showDialog = false
            },
            onDismissRequest = {
                println("Dialog dismissed")
                showDialog = false
            }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun NewOrderDialogExamplePreview() {
    NewOrderDialogExample()
}
