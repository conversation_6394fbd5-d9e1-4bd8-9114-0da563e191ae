package com.uniq.uniqpos.view.transaction.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.uniq.uniqpos.model.Customer
import com.uniq.uniqpos.model.NewOrder
import com.uniq.uniqpos.model.OrderItem

/**
 * Example usage of NewOrderDialog composable
 * This demonstrates how to integrate the dialog in a Compose UI
 */
@Composable
fun NewOrderDialogExample() {
    // State to control dialog visibility
    var showDialog by remember { mutableStateOf(false) }
    
    // Sample order data
    val sampleOrder = remember {
        NewOrder(
            id = "#E-7B3D9",
            customer = Customer(
                name = "<PERSON> Vance",
                phone = "+****************"
            ),
            items = listOf(
                OrderItem(
                    quantity = 2,
                    name = "Classic Cheeseburger",
                    modifiers = listOf("Extra pickles", "no onions"),
                    subtotal = 25.98
                ),
                OrderItem(
                    quantity = 1,
                    name = "Large Fries",
                    modifiers = emptyList(),
                    subtotal = 6.49
                ),
                OrderItem(
                    quantity = 2,
                    name = "Vanilla Milkshake",
                    modifiers = emptyList(),
                    subtotal = 11.98
                )
            ),
            table = "12A",
            total = 44.45
        )
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Button(
            onClick = { showDialog = true }
        ) {
            Text("Show New Order Dialog")
        }
    }
    
    // Conditional dialog display
    if (showDialog) {
        NewOrderDialog(
            order = sampleOrder,
            onAccept = {
                println("Order accepted: ${sampleOrder.id}")
                showDialog = false
            },
            onReject = {
                println("Order rejected: ${sampleOrder.id}")
                showDialog = false
            },
            onDismissRequest = {
                println("Dialog dismissed")
                showDialog = false
            }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun NewOrderDialogExamplePreview() {
    NewOrderDialogExample()
}
